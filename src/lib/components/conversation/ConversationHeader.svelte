<script lang="ts">
	import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge } from 'flowbite-svelte';
	import { EditOutline, TicketSolid, BugSolid } from 'flowbite-svelte-icons';
	import TransferTicketOwner from '$lib/components/UI/TransferTicketOwner.svelte';
	import ChangeTicketStatus from '$lib/components/UI/ChangeTicketStatus.svelte';
	import ChangeTicketPriority from '$lib/components/UI/ChangeTicketPriority.svelte';
	import { getPriorityClass, getStatusClass } from '$lib/utils';
	import { triggerRefresh } from '$lib/stores/refreshStore';
	import { onDestroy } from 'svelte';
	import { toastStore } from '$lib/stores/toastStore';
	import { services } from '$src/lib/api/features';
	import { selectedTicketData, ticketStore } from '$lib/stores/ticketStore';
	import { tabCacheStore } from '$lib/stores/tabCacheStore';

	import { page } from '$app/stores';

	export let customerId: number;
	export let customerName: string;
	export let channelName: string;
	export let connected: boolean | null = null;
	export let platformId: any = [];
	export let access_token: string;
	export let isReadOnly: boolean = false;
	export let users: any[];
	export let priorities: any[];
	export let statuses: any[];
	export let topics: any[];

	// Ticket data from centralized store
	$: ticketData = $selectedTicketData;
	$: ticket = ticketData?.ticket || null;
	$: ticketLoading = ticketData?.loading || false;
	$: ticketError = ticketData?.error || null;
	$: loginUser = ticketData?.loginUser || $page.data.id;

	import { t } from '$lib/stores/i18n';

	// State variables (removed ticket and loginUser since they come as props now)
	let currentPriorityName: string = ''; // Add this reactive variable
	let connectionInitialized = false;
	let connectionTimeout: ReturnType<typeof setTimeout> | null = null;
	let isCreatingTicket = false; // Loading state for new ticket creation

	// Reactive statement to update currentPriorityName when ticket prop changes
	$: currentPriorityName = ticket?.priority?.name || '';

	// Reactive variable to determine when Priority Badge should be read-only
	// Read-only when either: user doesn't own ticket OR user is not Admin/Supervisor
	$: isPriorityReadOnly = !ticket ||
	                        (ticket.owner_id !== loginUser) || // User doesn't own ticket
							(ticket.status.toLowerCase() === 'closed') || // Ticket is closed
							(ticket.status.toLowerCase() === 'pending_to_close'); // Ticket is pending to close


	// Reactive variable to determine when Owner Badge should be read-only
	// Read-only when ticket is closed or pending to close
	$: isOwnerReadOnly = !ticket ||
  						 (ticket.status.toLowerCase() === 'closed') || // Ticket is closed
						 (ticket.status.toLowerCase() === 'pending_to_close'); // Ticket is pending to close

	$: isStatusReadOnly = !ticket ||
	                  	  (ticket.owner_id !== loginUser && $page.data.role === 'Agent' && ticket.status.toLowerCase() === 'assigned') || // User is not owner and ticket is assigned, and user does not have Supervisor or Admin role
						  (ticket.owner.name === 'System') || // Ticket is owned by System
						  (ticket.status.toLowerCase() === 'closed') || // Ticket is closed
						  (ticket.status.toLowerCase() === 'pending_to_close'); // Ticket is pending to close

	$: canCreateNewTicket = ticket &&
	                        // (ticket.owner_id === loginUser) && // User doesn't own ticket
							((ticket.status.toLowerCase() === 'closed') || // Ticket is closed
							(ticket.status.toLowerCase() === 'pending_to_close')); // Ticket is pending to close

	// Debug reactive statements to track prop changes
	// $: if (ticket) {
	// 	console.log('ConversationHeader.svelte: ticket prop updated:', {
	// 		ticketId: ticket.id,
	// 		status: ticket.status?.name,
	// 		priority: ticket.priority?.name,
	// 		owner: ticket.owner?.name,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// All data fetching and polling logic removed - now handled by parent ConversationView.svelte

	async function createNewTicketConversation() {
		try {
			isCreatingTicket = true;

			// Validate required data
			if (!platformId) {
				toastStore.add(t('error_missing_platform_id') || 'Platform ID is required', 'error');
				return;
			}

			if (!access_token) {
				toastStore.add(t('error_missing_access_token') || 'Access token is required', 'error');
				return;
			}
			console.log(platformId);
			// Make API call to create new ticket using service
			const result = await services.tickets.initiateConversation(platformId, access_token);

			if (result.res_status !== 201) {
				console.error('Error creating new ticket:', result.error_msg);
				toastStore.add(t('chat_center_ticket_create_failed'), 'error');
				return;
			}

			// Extract the new ticket data from the response
			const newTicket = result.data?.ticket;
			if (!newTicket) {
				console.error('No ticket data in response:', result);
				toastStore.add(t('chat_center_ticket_create_failed'), 'error');
				return;
			}

			console.log('New ticket created successfully:', newTicket);

			// Update the centralized stores with the new ticket data
			const newTicketId = newTicket.id.toString();

			// 1. Update the ticket store with the new ticket data
			ticketStore.setTicketData(newTicketId, newTicket, loginUser);

			// 2. Update the tabCacheStore to select the new ticket for the current platform
			tabCacheStore.setSelectedPlatform(platformId, newTicketId);

			// 3. Trigger an immediate refresh of the platform list to show the new ticket
			await tabCacheStore.refreshAllTabs();

			// Show success message
			toastStore.add(t('chat_center_ticket_create_success'), 'success');

			// The centralized polling system will now automatically:
			// - Update PlatformIdentityList with the new ticket ID
			// - Provide the new ticket data to ConversationView and ConversationHeader
			// - Continue polling with the new ticket ID

		} catch (error) {
			console.error('Error creating new ticket conversation:', error);
			toastStore.add(t('chat_center_ticket_create_failed'), 'error');
		} finally {
			isCreatingTicket = false;
		}
	}

	function getOwnerStatusIndicator(status: string): string {
		switch (status.toLowerCase()) {
			case 'online':
				return 'bg-green-400';
			case 'away':
				return 'bg-yellow-400';
			case 'busy':
				return 'bg-red-400';
			case 'offline':
				return 'bg-gray-400';
			default:
				return 'hidden'; // Hide indicator for unknown status
		}
	}

	// Enhanced badge configuration functions
	function getTicketStatusBadgeConfig(id: number, status: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			closed: {
				class: getStatusClass(id),
				text: t('tickets_closed'),
				showIcon: true
			},
			open: {
				class: getStatusClass(id),
				text: t('tickets_open'),
				showIcon: false
			},
			assigned: {
				class: getStatusClass(id),
				text: t('tickets_assigned'),
				showIcon: false
			},
			waiting: {
				class: getStatusClass(id),
				text: t('tickets_waiting'),
				showIcon: false
			},
			pending_to_close: {
				class: getStatusClass(id),
				text: t('tickets_pending_to_close'),
				showIcon: false
			}
		};
		return configs[status?.toLowerCase()] || configs['none'];
	}

	function getPriorityBadgeConfig(priorityName: string) {
		const configs: Record<string, { class: string; text: string; showIcon: boolean }> = {
			none: {
				class: '',
				text: '',
				showIcon: false
			},
			Low: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_low'),
				showIcon: false
			},
			Medium: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_medium'),
				showIcon: true
			},
			High: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_high'),
				showIcon: true
			},
			Immediately: {
				class: getPriorityClass(priorityName),
				text: t('tickets_priority_immediately'),
				showIcon: true
			}
		};
		return configs[priorityName] || configs['none'];
	}

	// Lifecycle: Cleanup connection timeout when component is destroyed
	onDestroy(() => {
		// Clear connection timeout if it exists
		if (connectionTimeout) {
			clearTimeout(connectionTimeout);
		}
	});

	// Modal state variables
	let transferOwnerModalOpen = false;
	let changeStatusModalOpen = false;
	let changePriorityModalOpen = false;

	// Modal opening functions
	function openTransferOwnerModal() {
		if (ticket) {
			transferOwnerModalOpen = true;
		}
	}

	function openChangeStatusModal() {
		if (ticket) {
			changeStatusModalOpen = true;
		}
	}

	function openChangePriorityModal() {
		if (ticket) {
			changePriorityModalOpen = true;
		}
	}

	// Function to refresh ticket data after operations
	// Note: The actual data fetching is now handled by the parent ConversationView.svelte
	// This function just triggers a global refresh to update all components
	function refreshTicketData() {
		// Trigger global refresh to update platform identity list and other components
		triggerRefresh();
	}

	// Reactive badge configurations
	$: statusBadgeConfig = getTicketStatusBadgeConfig(ticket?.status_id, ticket?.status);
	$: priorityBadgeConfig = getPriorityBadgeConfig(currentPriorityName);
	// Modify the reactive statement to track when connection status is first determined
	$: if (connected !== null) {
		connectionInitialized = false;
		// Clear any existing timeout
		if (connectionTimeout) {
			clearTimeout(connectionTimeout);
		}

		// Set a minimum display time for the connecting state (500ms)
		connectionTimeout = setTimeout(() => {
			connectionInitialized = true;
		}, 500);
	}
</script>

<div
	id="conv-header-conversation-header"
	class="min-h-{isReadOnly ? '[90px]' : '[120px]'} border-b border-gray-200 bg-white px-4 py-4 sm:px-6"
>
	<div class="flex flex-row space-y-4 justify-between gap-4">
		<!-- Left: Avatar + Customer Info + Status Badges -->
		<div class="flex flex-col space-y-3">
			<!-- Avatar + Customer Info Row -->
			<div class="flex items-center space-x-3">
				<!-- Avatar -->
				<!-- <div class="flex-shrink-0">
					<div
						class="flex h-10 w-10 items-center justify-center rounded-full bg-blue-500 text-sm font-semibold text-white"
						role="img"
						aria-label="Customer avatar for {customerName}"
					>
						{getInitials(customerName)}
					</div>
				</div> -->
				<!-- Customer Info -->
				<div class="flex min-w-0 flex-col">
					<h2 id="conv-header-customer-name" class="truncate text-lg font-semibold text-gray-900">
						{customerName}
					</h2>
					<p id="conv-header-channel-info" class="truncate text-sm text-gray-500">
						{t('chat_center_filter_channel')}: {channelName}
					</p>
				</div>
			</div>
		</div>

		<!-- Right: Timestamp + Actions -->
		<div
			class="flex flex-row self-end justify-end space-y-2 items-center space-x-4"
		>
			<!-- Last Activity -->
			<!-- {#if ticket?.updated_at}
				<div class="flex items-center text-sm text-gray-500">
					<ClockSolid class="mr-1 h-4 w-4" aria-hidden="true" />
					<span class="whitespace-nowrap"
						>Last activity: {timeAgo(ticket.updated_at, ticket?.status || '')}</span
					>
				</div>
			{/if} -->

			<!-- Actions - Removed dropdown menu -->
			<div class="flex justify-end gap-2 min-w-[150px]">
				<!-- Actions will be handled by clickable badges -->
				{#if !isReadOnly && canCreateNewTicket}
					<Button
						id="conv-header-button-create-ticket"
						size="xs"
						class=""
						color="blue"
						disabled={isCreatingTicket}
						on:click={() => createNewTicketConversation()}
					>
						{#if isCreatingTicket}
							<span class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
							{t('chat_center_ticket_creating')}
						{:else}
							<TicketSolid class="mr-2 h-4 w-4" />
							{t('chat_center_ticket_create')}
						{/if}
					</Button>
				{/if}
			</div>
		</div>
	</div>

	<!-- Status Badges Row -->
	{#if !isReadOnly}
		<div class="mt-3 flex w-full gap-1 items-center justify-between">
			<!-- Left side badges group -->
			<div class="flex flex-wrap items-center gap-2">
					{#if !ticketLoading && ticket}
						<!-- Ticket ID -->
						<!-- <div
							id="conv-header-ticket-id"
							class="flex items-center space-x-1 rounded px-1 py-1 text-xs text-gray-600"
						>
							<TicketSolid class="h-4 w-4" />
							<span class="whitespace-nowrap font-bold">{ticket.id}</span>
						</div> -->

						<!-- Status Badge - Clickable -->
						{#if statusBadgeConfig.text !== ''}
							{#if isReadOnly || isStatusReadOnly}
								<div
									id="conv-header-badge-status"
									class="flex items-center space-x-1 rounded px-2 py-1 text-xs {statusBadgeConfig.class}"
								>
									<span class="whitespace-nowrap text-xs">
										{t('table_status')}:
									</span>
									<span class="whitespace-nowrap text-xs font-bold">
										{statusBadgeConfig.text}
									</span>
								</div>
							{:else}
								<button
									id="conv-header-badge-status"
									class="flex items-center space-x-1 rounded px-2 py-1 {statusBadgeConfig.class} cursor-pointer transition-all duration-200 hover:opacity-80 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500"
									aria-label="Click to change ticket status: {statusBadgeConfig.text}"
									on:click={openChangeStatusModal}
									on:keydown={(e) => e.key === 'Enter' && openChangeStatusModal()}
									tabindex="0"
								>
									<EditOutline class="h-4 w-4" aria-hidden="true" />
									<span class="whitespace-nowrap text-xs">
										{t('table_status')}:
									</span>
									<span class="whitespace-nowrap text-xs font-bold">
										{statusBadgeConfig.text}
									</span>
								</button>
							{/if}
						{/if}

						<!-- Priority Badge - Clickable -->
						{#if priorityBadgeConfig.text !== ''}
							{#if isReadOnly || isPriorityReadOnly}
								<div
									id="conv-header-badge-priority"
									class="flex items-center space-x-1 rounded px-2 py-1 text-xs {priorityBadgeConfig.class}"
								>
									<span class="whitespace-nowrap text-xs">
										{t('table_priority')}:
									</span>
									<span class="whitespace-nowrap text-xs font-bold">
										{priorityBadgeConfig.text}
									</span>
								</div>
							{:else}
								<button
									id="conv-header-badge-priority"
								class="flex items-center space-x-1 rounded px-2 py-1 {priorityBadgeConfig.class} cursor-pointer transition-all duration-200 hover:opacity-80 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500"
								aria-label="Click to change ticket priority: {priorityBadgeConfig.text}"
								on:click={openChangePriorityModal}
								on:keydown={(e) => e.key === 'Enter' && openChangePriorityModal()}
								tabindex="0"
							>
								<EditOutline class="h-4 w-4" aria-hidden="true" />
								<span class="whitespace-nowrap text-xs">
									{t('table_priority')}:
								</span>
								<span class="whitespace-nowrap text-xs font-bold">
									{priorityBadgeConfig.text}
								</span>
							</button>
							{/if}
						{/if}
					{:else}
						<!-- Loading state for badges -->
						<div
							id="conv-header-badge-loading"
							class="flex items-center space-x-1 rounded bg-gray-100 px-2 py-1"
						>
							<span class="whitespace-nowrap text-xs font-medium text-gray-500">
								{t('loading')}...
							</span>
						</div>
					{/if}
					<!-- For Debugging -->
					<!-- <Badge color="purple">
						<BugSolid class="mr-1 h-4 w-4" /> Customer ID: {customerId} | CPI: {platformId} | Ticket ID: {ticket? ticket.id : 'N/A'} 
					</Badge> -->
			</div>

			<div class="flex flex-wrap items-center justify-end gap-2">
				<!-- Owner Badge - Clickable -->
				{#if !ticketLoading && ticket}
					{#if isReadOnly || isOwnerReadOnly}
						<div
							id="conv-header-badge-owner"
							class="flex items-center space-x-1 rounded px-2 py-1 text-xs border border-gray-600"
						>
							<span class="whitespace-nowrap text-xs">
								{t('table_agent')}:
							</span>
							<div
								id="owner-status-indicator-{ticket.id}"
								class="h-2 w-2 rounded-full {getOwnerStatusIndicator(ticket.owner?.status)}"
								aria-hidden="true"
							/>
							<span class="whitespace-nowrap text-xs font-bold">
								{ticket.owner?.name}
							</span>
						</div>
					{:else}
						<button
							id="conv-header-badge-owner"
							class="flex cursor-pointer items-center space-x-1 rounded border border-gray-600 px-2 py-1 transition-all duration-200 hover:bg-gray-50 hover:shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500"
							aria-label="Click to transfer ticket owner: {ticket.owner?.name}"
							on:click={openTransferOwnerModal}
							on:keydown={(e) => e.key === 'Enter' && openTransferOwnerModal()}
							tabindex="0"
						>
							<EditOutline class="h-4 w-4" aria-hidden="true" />
							<span class="whitespace-nowrap text-xs">
								{t('table_agent')}:
							</span>
							<div
								id="owner-status-indicator-{ticket.id}"
								class="h-2 w-2 rounded-full {getOwnerStatusIndicator(ticket.owner?.status)}"
								aria-hidden="true"
							/>
							<span class="whitespace-nowrap text-xs font-bold">
								{ticket.owner?.name}
							</span>
						</button>
					{/if}

					<!-- Tooltips for status indicators -->
					{#if ticket?.owner?.status}
						<Tooltip 
							triggeredBy="#owner-status-indicator-{ticket.id}" 
							placement="bottom" 
							class="z-50 text-xs bg-gray-600"
						>
							{t(ticket.owner?.status)}
						</Tooltip>
					{/if}
				{/if}
				<!-- Connection Badge (right-aligned) -->
				<div
					id="conv-header-badge-connection"
					class="flex cursor-default items-center space-x-1 rounded border px-2 py-1 {connected
							? 'border-green-500 text-green-700'
							: 'border-gray-500 text-gray-700'}"
					role="status"
					aria-label="Connection status: {connected
							? t('connected')
							: t('connecting')}"
				>
					<div
						class="h-2 w-2 animate-pulse rounded-full {connected ? 'bg-green-500' : 'bg-gray-500'}"
						aria-hidden="true"
					/>
					<span class="whitespace-nowrap text-xs font-bold">
						{connected ? t('connected') : t('connecting')}
					</span>
				</div>
			</div>
		</div>
	{/if}
</div>

<!-- Modal Components - Using bindable props for modal state -->
<!-- Transfer Ticket Owner Modal -->
<TransferTicketOwner
	ticket={ticket ? { ...ticket, id: ticket.id } : null}
	{users}
	loggedInUsername={$page.data.username}
	loggedInRole={$page.data.role}
	isDropDownItem={false}
	showButton={false}
	bind:modalOpen={transferOwnerModalOpen}
	onSuccess={refreshTicketData}
/>

<!-- Change Ticket Status Modal -->
<ChangeTicketStatus
	ticket={ticket ? { ...ticket, id: ticket.id } : null}
	{statuses}
	ticket_topics={topics}
	loginUser={loginUser}
	isDropDownItem={false}
	showButton={false}
	bind:modalOpen={changeStatusModalOpen}
	onSuccess={refreshTicketData}
/>

<!-- Change Ticket Priority Modal -->
<ChangeTicketPriority
	ticket={ticket ? { ...ticket, id: ticket.id } : null}
	{priorities}
	isDropDownItem={false}
	showButton={false}
	bind:modalOpen={changePriorityModalOpen}
	onSuccess={refreshTicketData}
/>
